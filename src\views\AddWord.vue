<template>
  <div class="add-word-container">
    <h2 class="page-title">{{ isEditing ? '编辑单词' : '添加单词' }}</h2>

    <!-- 数据管理功能区域 -->
    <div v-if="!isEditing" class="data-management-section">
      <el-tabs type="border-card" class="management-tabs">
        <!-- 导入功能 -->
        <el-tab-pane label="批量导入">
          <div class="import-content">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><Upload /></el-icon>
                批量导入单词
              </h3>
              <p class="help-text">支持导入Excel和CSV格式文件，已存在的单词将被自动忽略</p>
            </div>

            <div class="upload-info">
              <el-alert
                title="支持的文件格式"
                type="info"
                :closable="false"
                show-icon
              >
                <p><strong>Excel格式：</strong>支持.xlsx和.xls文件</p>
                <p><strong>CSV格式：</strong>支持.csv文件</p>
                <p><strong>文件要求：</strong></p>
                <ul>
                  <li>第一列：单词拼写 (spelling)</li>
                  <li>第二列：单词释义 (definition)</li>
                  <li>第三列：例句 (example，可选)</li>
                </ul>
              </el-alert>
            </div>

            <el-upload
              ref="uploadRef"
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :before-remove="beforeRemove"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls,.csv,.json"
              :limit="10"
              multiple
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持 Excel (.xlsx, .xls)、CSV (.csv) 和 JSON (.json) 格式文件，可多选
                </div>
              </template>
            </el-upload>

            <div class="import-actions">
              <el-button
                type="success"
                @click="importWords"
                :disabled="!selectedFiles.length"
                :loading="importing"
              >
                开始导入
              </el-button>
              <el-button @click="downloadTemplate">下载Excel模板</el-button>
              <el-button @click="downloadCSVTemplate">下载CSV模板</el-button>
            </div>

            <!-- 导入预览 -->
            <div v-if="previewData.length > 0" class="preview-section">
              <h4>导入预览 (前5条)</h4>
              <el-table :data="previewData.slice(0, 5)" border>
                <el-table-column prop="spelling" label="单词" width="120" />
                <el-table-column prop="definition" label="释义" />
                <el-table-column prop="example" label="例句" />
              </el-table>
              <p class="preview-info">共 {{ previewData.length }} 条数据</p>
            </div>
          </div>
        </el-tab-pane>

        <!-- 导出功能 -->
        <el-tab-pane label="导出词库">
          <div class="export-content">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><Download /></el-icon>
                导出词库
              </h3>
              <p class="help-text">将您的词库导出为不同格式的文件进行备份或分享</p>
            </div>

            <div class="export-options">
              <div class="export-option">
                <div class="option-info">
                  <h4>Excel格式 (.xlsx)</h4>
                  <p>适合在Excel中查看和编辑，支持格式化</p>
                </div>
                <el-button
                  type="primary"
                  @click="exportToExcel"
                  :disabled="!hasWords"
                  plain
                >
                  <el-icon><Download /></el-icon>
                  导出Excel
                </el-button>
              </div>

              <div class="export-option">
                <div class="option-info">
                  <h4>CSV格式 (.csv)</h4>
                  <p>通用格式，可在多种软件中打开</p>
                </div>
                <el-button
                  type="success"
                  @click="exportToCSV"
                  :disabled="!hasWords"
                  plain
                >
                  <el-icon><Download /></el-icon>
                  导出CSV
                </el-button>
              </div>

              <div class="export-option">
                <div class="option-info">
                  <h4>JSON格式 (.json)</h4>
                  <p>完整的数据备份，包含所有字段信息</p>
                </div>
                <el-button
                  type="info"
                  @click="exportToJSON"
                  :disabled="!hasWords"
                  plain
                >
                  <el-icon><Download /></el-icon>
                  导出JSON
                </el-button>
              </div>
            </div>

            <div v-if="!hasWords" class="no-data-tip">
              <el-alert
                title="暂无数据"
                description="词库为空，请先添加一些单词后再导出"
                type="warning"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 单个添加表单 -->
    <el-form
      :model="wordForm"
      :rules="rules"
      ref="wordFormRef"
      label-width="100px"
      class="word-form"
    >
      <el-form-item label="单词" prop="spelling">
        <el-input v-model="wordForm.spelling" placeholder="请输入单词" />
      </el-form-item>

      <el-form-item label="发音" class="pronunciation-item">
        <el-button @click="playPronunciation" :disabled="!wordForm.spelling">
          <el-icon><VideoPlay /></el-icon>
          播放发音
        </el-button>
      </el-form-item>

      <el-form-item label="释义" prop="definition">
        <el-input
          v-model="wordForm.definition"
          type="textarea"
          rows="3"
          placeholder="请输入单词释义"
        />
      </el-form-item>

      <el-form-item label="例句">
        <el-input
          v-model="wordForm.example"
          type="textarea"
          rows="3"
          placeholder="请输入例句（可选）"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { VideoPlay, Upload, Download } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

const store = useStore()
const router = useRouter()
const route = useRoute()
const wordFormRef = ref(null)
const uploadRef = ref(null)

const isEditing = computed(() => route.params.id !== undefined)

// 导入相关状态
const selectedFiles = ref([])
const importing = ref(false)
const previewData = ref([])

// 计算属性
const hasWords = computed(() => store.state.words.length > 0)

const wordForm = reactive({
  id: '',
  spelling: '',
  definition: '',
  example: ''
})

const rules = {
  spelling: [
    { required: true, message: '请输入单词', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  definition: [
    { required: true, message: '请输入释义', trigger: 'blur' }
  ]
}

onMounted(() => {
  if (isEditing.value) {
    const id = route.params.id
    const word = store.state.words.find(w => w.id === id)
    
    if (word) {
      wordForm.id = word.id
      wordForm.spelling = word.spelling
      wordForm.definition = word.definition
      wordForm.example = word.example || ''
    } else {
      ElMessage.error('未找到要编辑的单词')
      router.push('/')
    }
  }
})

const playPronunciation = () => {
  if (wordForm.spelling) {
    const utterance = new SpeechSynthesisUtterance(wordForm.spelling)
    utterance.lang = 'en-US'
    window.speechSynthesis.speak(utterance)
  }
}

const submitForm = () => {
  wordFormRef.value.validate((valid) => {
    if (valid) {
      if (isEditing.value) {
        store.commit('updateWord', { ...wordForm })
        ElMessage.success('单词更新成功')
      } else {
        const newWord = { 
          ...wordForm,
          id: Date.now().toString(), 
          createdAt: new Date()
        }
        store.commit('addWord', newWord)
        ElMessage.success('单词添加成功')
      }
      router.push('/')
    }
  })
}

const goBack = () => {
  router.push('/')
}

// 文件处理相关方法
const handleFileChange = (file, fileList) => {
  console.log('文件选择:', file)
  selectedFiles.value = fileList.map(f => f.raw)
  if (fileList.length > 0) {
    previewFile(fileList[0].raw)
  } else {
    previewData.value = []
  }
}

const beforeUpload = (file) => {
  console.log('文件上传前检查:', file)
  const isValidType = file.name.toLowerCase().endsWith('.csv') ||
                     file.name.toLowerCase().endsWith('.xlsx') ||
                     file.name.toLowerCase().endsWith('.xls') ||
                     file.name.toLowerCase().endsWith('.json')

  if (!isValidType) {
    ElMessage.error('只支持 CSV、Excel、JSON 文件格式！')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传，我们手动处理
}

const beforeRemove = () => {
  selectedFiles.value = []
  previewData.value = []
  return true
}

// 预览文件内容
const previewFile = async (file) => {
  try {
    console.log('开始预览文件:', file.name, file.type, file.size)
    const data = await readFile(file)
    previewData.value = data
    console.log('预览数据:', data)
    if (data.length === 0) {
      ElMessage.warning('文件中没有找到有效的单词数据')
    }
  } catch (error) {
    console.error('文件读取错误:', error)
    ElMessage.error('文件读取失败: ' + error.message)
    previewData.value = []
  }
}

// 读取文件内容
const readFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = e.target.result
        let jsonData = []

        if (file.name.endsWith('.csv')) {
          // 处理CSV文件
          jsonData = parseCSV(data)
        } else {
          // 处理Excel文件
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          jsonData = parseExcelData(jsonData)
        }

        resolve(jsonData)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('文件读取失败'))

    if (file.name.endsWith('.csv')) {
      reader.readAsText(file, 'UTF-8')
    } else {
      reader.readAsArrayBuffer(file)
    }
  })
}

// 解析CSV数据
const parseCSV = (csvText) => {
  console.log('开始解析CSV:', csvText.substring(0, 200))
  const lines = csvText.split('\n').filter(line => line.trim())
  const result = []

  if (lines.length === 0) {
    throw new Error('CSV文件为空')
  }

  // 跳过标题行（如果存在）
  const firstLine = lines[0].toLowerCase()
  const startIndex = (firstLine.includes('单词') || firstLine.includes('word') || firstLine.includes('definition')) ? 1 : 0

  console.log('CSV行数:', lines.length, '开始行:', startIndex)

  for (let i = startIndex; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    // 简单的CSV解析，处理逗号分隔
    const columns = []
    let current = ''
    let inQuotes = false

    for (let j = 0; j < line.length; j++) {
      const char = line[j]
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        columns.push(current.trim().replace(/^"|"$/g, ''))
        current = ''
      } else {
        current += char
      }
    }
    columns.push(current.trim().replace(/^"|"$/g, ''))

    console.log(`第${i}行解析结果:`, columns)

    if (columns.length >= 2 && columns[0] && columns[1]) {
      result.push({
        spelling: columns[0],
        definition: columns[1],
        example: columns[2] || ''
      })
    }
  }

  console.log('CSV解析完成，共', result.length, '条数据')
  return result
}

// 解析Excel数据
const parseExcelData = (data) => {
  const result = []

  // 跳过标题行（如果存在）
  const startIndex = data[0] && (data[0].includes('单词') || data[0].includes('word')) ? 1 : 0

  for (let i = startIndex; i < data.length; i++) {
    const row = data[i]
    if (row && row.length >= 2 && row[0] && row[1]) {
      result.push({
        spelling: String(row[0]).trim(),
        definition: String(row[1]).trim(),
        example: row[2] ? String(row[2]).trim() : ''
      })
    }
  }

  return result
}

// 导入单词
const importWords = async () => {
  if (!selectedFiles.value.length) {
    ElMessage.warning('请先选择文件')
    return
  }

  importing.value = true
  let totalImported = 0
  let totalIgnored = 0
  let totalFiles = 0

  try {
    // 获取当前词库
    const currentWords = store.state.words
    let existingSpellings = new Set(currentWords.map(word => word.spelling.toLowerCase()))
    const allNewWords = []

    for (const file of selectedFiles.value) {
      try {
        let data = []
        const fileName = file.name.toLowerCase()

        if (fileName.endsWith('.json')) {
          // 处理JSON文件
          const fileContent = await readFileAsText(file)
          const jsonData = JSON.parse(fileContent)
          if (Array.isArray(jsonData)) {
            data = jsonData
          } else {
            throw new Error('JSON文件格式不正确，应该是单词数组')
          }
        } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
          // 处理Excel文件
          data = await readExcelFile(file)
        } else if (fileName.endsWith('.csv')) {
          // 处理CSV文件
          data = await readCSVFile(file)
        } else {
          ElMessage.warning(`不支持的文件格式：${file.name}`)
          continue
        }

        if (data.length > 0) {
          totalFiles++

          // 过滤出尚未存在的单词
          const newWords = data.filter(word =>
            word.spelling &&
            word.definition &&
            !existingSpellings.has(word.spelling.toLowerCase())
          ).map(word => ({
            ...word,
            id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
            createdAt: new Date()
          }))

          // 更新统计数据
          totalImported += newWords.length
          totalIgnored += data.length - newWords.length

          // 如果有新单词，则收集起来
          if (newWords.length > 0) {
            allNewWords.push(...newWords)
            // 更新存在的拼写集合，防止后续文件中的重复单词被导入
            newWords.forEach(word => existingSpellings.add(word.spelling.toLowerCase()))
          }
        }
      } catch (error) {
        console.error('解析文件失败：', error)
        ElMessage.error(`解析文件 ${file.name} 失败：${error.message}`)
      }
    }

    // 更新词库
    if (allNewWords.length > 0) {
      store.commit('addWords', allNewWords)
      ElMessage.success(`已处理 ${totalFiles} 个文件，成功导入 ${totalImported} 个新单词，已忽略 ${totalIgnored} 个已存在的单词`)
    } else {
      ElMessage.info('没有导入新单词，所有单词都已存在')
    }

    // 清空文件选择
    selectedFiles.value = []
    previewData.value = []
    uploadRef.value?.clearFiles()

  } catch (error) {
    ElMessage.error('导入失败：' + error.message)
  } finally {
    importing.value = false
  }
}

// 文件读取辅助函数
const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsText(file, 'UTF-8')
  })
}

const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsArrayBuffer(file)
  })
}

// 读取Excel文件
const readExcelFile = async (file) => {
  try {
    const arrayBuffer = await readFileAsArrayBuffer(file)
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (jsonData.length === 0) {
      throw new Error('Excel文件为空')
    }

    const words = []
    const startRow = jsonData[0] && typeof jsonData[0][0] === 'string' &&
                     (jsonData[0][0].toLowerCase().includes('spelling') ||
                      jsonData[0][0].toLowerCase().includes('单词') ||
                      jsonData[0][0].toLowerCase().includes('word')) ? 1 : 0

    for (let i = startRow; i < jsonData.length; i++) {
      const row = jsonData[i]
      if (row && row.length >= 2 && row[0] && row[1]) {
        const word = {
          spelling: String(row[0]).trim(),
          definition: String(row[1]).trim(),
          example: row[2] ? String(row[2]).trim() : ''
        }

        if (word.spelling && word.definition) {
          words.push(word)
        }
      }
    }

    if (words.length === 0) {
      throw new Error('Excel文件中没有找到有效的单词数据')
    }

    return words
  } catch (error) {
    throw new Error(`Excel文件解析失败：${error.message}`)
  }
}

// 读取CSV文件
const readCSVFile = async (file) => {
  try {
    const csvText = await readFileAsText(file)
    const lines = csvText.split('\n').filter(line => line.trim())

    if (lines.length === 0) {
      throw new Error('CSV文件为空')
    }

    const words = []
    const firstLine = lines[0].toLowerCase()
    const startIndex = (firstLine.includes('单词') || firstLine.includes('word') || firstLine.includes('spelling')) ? 1 : 0

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      // 简单的CSV解析
      const columns = []
      let current = ''
      let inQuotes = false

      for (let j = 0; j < line.length; j++) {
        const char = line[j]
        if (char === '"') {
          inQuotes = !inQuotes
        } else if (char === ',' && !inQuotes) {
          columns.push(current.trim().replace(/^"|"$/g, ''))
          current = ''
        } else {
          current += char
        }
      }
      columns.push(current.trim().replace(/^"|"$/g, ''))

      if (columns.length >= 2 && columns[0] && columns[1]) {
        words.push({
          spelling: columns[0],
          definition: columns[1],
          example: columns[2] || ''
        })
      }
    }

    if (words.length === 0) {
      throw new Error('CSV文件中没有找到有效的单词数据')
    }

    return words
  } catch (error) {
    throw new Error(`CSV文件解析失败：${error.message}`)
  }
}

// 下载Excel模板
const downloadTemplate = () => {
  const templateData = [
    ['单词', '释义', '例句'],
    ['hello', '你好', 'Hello, how are you?'],
    ['world', '世界', 'Welcome to the world'],
    ['example', '例子', 'This is an example']
  ]

  const ws = XLSX.utils.aoa_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Words')

  // 设置列宽
  ws['!cols'] = [
    { wch: 15 }, // 单词列
    { wch: 30 }, // 释义列
    { wch: 40 }  // 例句列
  ]

  XLSX.writeFile(wb, 'words_template.xlsx')
  ElMessage.success('Excel模板文件已下载')
}

// 下载CSV模板
const downloadCSVTemplate = () => {
  const csvContent = [
    '单词,释义,例句',
    'hello,你好,"Hello, how are you?"',
    'world,世界,Welcome to the world',
    'example,例子,This is an example'
  ].join('\n')

  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  link.setAttribute('href', url)
  link.setAttribute('download', 'words_template.csv')
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success('CSV模板文件已下载')
}

// 导出为Excel文件
const exportToExcel = () => {
  const words = store.state.words
  if (words.length === 0) {
    ElMessage.warning('词库为空，没有可导出的单词')
    return
  }

  // 准备数据
  const exportData = [
    ['单词', '释义', '例句', '创建时间']
  ]

  words.forEach(word => {
    exportData.push([
      word.spelling,
      word.definition,
      word.example || '',
      word.createdAt ? new Date(word.createdAt).toLocaleDateString() : ''
    ])
  })

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(exportData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '单词库')

  // 设置列宽
  ws['!cols'] = [
    { wch: 15 }, // 单词列
    { wch: 30 }, // 释义列
    { wch: 40 }, // 例句列
    { wch: 15 }  // 创建时间列
  ]

  // 设置标题行样式
  const headerStyle = {
    font: { bold: true },
    fill: { fgColor: { rgb: "EEEEEE" } }
  }

  ['A1', 'B1', 'C1', 'D1'].forEach(cell => {
    if (ws[cell]) {
      ws[cell].s = headerStyle
    }
  })

  const fileName = `mywords_${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)

  ElMessage.success(`词库已导出为Excel文件: ${fileName}`)
}

// 导出为CSV文件
const exportToCSV = () => {
  const words = store.state.words
  if (words.length === 0) {
    ElMessage.warning('词库为空，没有可导出的单词')
    return
  }

  // 准备CSV内容
  const csvRows = ['单词,释义,例句,创建时间']

  words.forEach(word => {
    const row = [
      `"${word.spelling}"`,
      `"${word.definition}"`,
      `"${word.example || ''}"`,
      `"${word.createdAt ? new Date(word.createdAt).toLocaleDateString() : ''}"`
    ]
    csvRows.push(row.join(','))
  })

  const csvContent = csvRows.join('\n')
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  const fileName = `mywords_${new Date().toISOString().split('T')[0]}.csv`
  link.setAttribute('href', url)
  link.setAttribute('download', fileName)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success(`词库已导出为CSV文件: ${fileName}`)
}

// 导出为JSON文件
const exportToJSON = () => {
  const words = store.state.words
  if (words.length === 0) {
    ElMessage.warning('词库为空，没有可导出的单词')
    return
  }

  const dataStr = JSON.stringify(words, null, 2)
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)

  const fileName = `mywords_${new Date().toISOString().split('T')[0]}.json`

  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', dataUri)
  linkElement.setAttribute('download', fileName)
  linkElement.click()

  ElMessage.success(`词库已导出为JSON文件: ${fileName}`)
}
</script>

<style scoped>
.add-word-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 0;
}

.page-title {
  margin-bottom: 30px;
  text-align: center;
  color: #409EFF;
}

.data-management-section {
  margin-bottom: 30px;
}

.management-tabs {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.management-tabs :deep(.el-tabs__header) {
  background: linear-gradient(90deg, #f8f9fa, #ffffff);
  margin: 0;
  border-radius: 12px 12px 0 0;
}

.management-tabs :deep(.el-tabs__item) {
  font-weight: 600;
  padding: 16px 24px;
}

.management-tabs :deep(.el-tabs__item.is-active) {
  color: #409EFF;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
  font-weight: 600;
}

.help-text {
  color: #666;
  margin: 8px 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.upload-info {
  margin-bottom: 20px;
}

.upload-info .el-alert {
  border-radius: 8px;
  text-align: left;
}

.upload-info p {
  margin: 8px 0;
  font-size: 14px;
}

.upload-info ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.upload-info li {
  margin: 4px 0;
  font-size: 13px;
}

.import-content, .export-content {
  padding: 20px;
}

.upload-demo {
  margin-bottom: 20px;
}

.import-actions {
  margin: 20px 0;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.preview-section {
  margin-top: 20px;
  text-align: left;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.export-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.export-option:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.option-info {
  flex: 1;
}

.option-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.option-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.no-data-tip {
  margin-top: 20px;
}

.preview-info {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-word-container {
    padding: 15px;
  }

  .management-tabs :deep(.el-tabs__item) {
    padding: 12px 16px;
    font-size: 14px;
  }

  .import-content, .export-content {
    padding: 15px;
  }

  .export-option {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .export-option .el-button {
    width: 100%;
  }

  .import-actions {
    flex-direction: column;
    gap: 10px;
  }

  .import-actions .el-button {
    width: 100%;
  }
}

.preview-section h4 {
  color: #409EFF;
  margin-bottom: 10px;
}

.preview-info {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.word-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pronunciation-item {
  margin-bottom: 22px;
}

.el-upload__tip {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}
</style>