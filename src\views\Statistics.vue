<template>
  <div class="statistics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><TrendCharts /></el-icon>
        学习统计
      </h2>
    </div>

    <!-- 总体统计卡片 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon words">
                <el-icon><Reading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ totalStats.totalWordsLearned }}</div>
                <div class="stat-label">累计学习</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon time">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ formatTime(totalStats.totalTimeSpent) }}</div>
                <div class="stat-label">学习时长</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon review">
                <el-icon><Refresh /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ totalStats.totalReviewCount }}</div>
                <div class="stat-label">复习次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon streak">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ totalStats.streakDays }}</div>
                <div class="stat-label">连续天数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 日历打卡 -->
    <div class="calendar-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>学习日历</span>
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              placeholder="选择月份"
              format="YYYY年MM月"
              value-format="YYYY-MM"
              @change="onMonthChange"
              size="small"
            />
          </div>
        </template>
        
        <div class="calendar-container">
          <div class="calendar-grid">
            <div class="calendar-header">
              <div v-for="day in weekDays" :key="day" class="day-header">{{ day }}</div>
            </div>
            <div class="calendar-body">
              <div 
                v-for="date in calendarDates" 
                :key="date.dateStr"
                class="calendar-day"
                :class="{
                  'other-month': !date.isCurrentMonth,
                  'today': date.isToday,
                  'has-record': date.hasRecord,
                  'high-activity': date.activity === 'high',
                  'medium-activity': date.activity === 'medium',
                  'low-activity': date.activity === 'low'
                }"
                @click="showDayDetail(date)"
              >
                <div class="day-number">{{ date.day }}</div>
                <div v-if="date.hasRecord" class="activity-indicator">
                  <div class="words-count">{{ date.wordsLearned }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习趋势图表 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>学习趋势</span>
                <el-radio-group v-model="chartPeriod" size="small">
                  <el-radio-button label="week">最近7天</el-radio-button>
                  <el-radio-button label="month">最近30天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div ref="chartContainer" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 日详情对话框 -->
    <el-dialog
      v-model="dayDetailVisible"
      :title="`${selectedDate} 学习详情`"
      width="400px"
    >
      <div v-if="selectedDayData" class="day-detail">
        <div class="detail-item">
          <span class="detail-label">学习单词：</span>
          <span class="detail-value">{{ selectedDayData.wordsLearned }} 个</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">学习时长：</span>
          <span class="detail-value">{{ formatTime(selectedDayData.timeSpent) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">复习次数：</span>
          <span class="detail-value">{{ selectedDayData.reviewCount }} 次</span>
        </div>
      </div>
      <div v-else class="no-data">
        该日期暂无学习记录
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useStore } from 'vuex'
import { 
  TrendCharts, 
  Reading, 
  Timer, 
  Refresh, 
  Trophy 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const store = useStore()

// 响应式数据
const selectedMonth = ref(new Date().toISOString().slice(0, 7))
const chartPeriod = ref('week')
const dayDetailVisible = ref(false)
const selectedDate = ref('')
const selectedDayData = ref(null)
const chartContainer = ref(null)
let chartInstance = null

// 计算属性
const totalStats = computed(() => store.state.learningStats.totalStats)
const dailyRecords = computed(() => store.state.learningStats.dailyRecords)

const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 格式化时间
const formatTime = (seconds) => {
  if (!seconds) return '0分钟'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

// 生成日历数据
const calendarDates = computed(() => {
  const year = parseInt(selectedMonth.value.split('-')[0])
  const month = parseInt(selectedMonth.value.split('-')[1])
  
  const firstDay = new Date(year, month - 1, 1)
  const lastDay = new Date(year, month, 0)
  const firstDayOfWeek = firstDay.getDay()
  
  const dates = []
  const today = new Date().toISOString().split('T')[0]
  
  // 添加上个月的日期
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const date = new Date(firstDay)
    date.setDate(date.getDate() - i - 1)
    dates.push(createDateObject(date, false, today))
  }
  
  // 添加当月的日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(year, month - 1, day)
    dates.push(createDateObject(date, true, today))
  }
  
  // 添加下个月的日期，补齐6行
  const remainingDays = 42 - dates.length
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(year, month, day)
    dates.push(createDateObject(date, false, today))
  }
  
  return dates
})

const createDateObject = (date, isCurrentMonth, today) => {
  const dateStr = date.toISOString().split('T')[0]
  const record = dailyRecords.value[dateStr]
  
  let activity = 'none'
  if (record && record.wordsLearned > 0) {
    if (record.wordsLearned >= 20) activity = 'high'
    else if (record.wordsLearned >= 10) activity = 'medium'
    else activity = 'low'
  }
  
  return {
    date,
    dateStr,
    day: date.getDate(),
    isCurrentMonth,
    isToday: dateStr === today,
    hasRecord: !!record && record.wordsLearned > 0,
    wordsLearned: record ? record.wordsLearned : 0,
    activity,
    data: record
  }
}

// 方法
const onMonthChange = () => {
  // 月份改变时重新计算日历
}

const showDayDetail = (date) => {
  if (date.hasRecord) {
    selectedDate.value = date.dateStr
    selectedDayData.value = date.data
    dayDetailVisible.value = true
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  
  const days = chartPeriod.value === 'week' ? 7 : 30
  const dates = []
  const wordsData = []
  const timeData = []
  const reviewData = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]
    const record = dailyRecords.value[dateStr]
    
    dates.push(date.getMonth() + 1 + '/' + date.getDate())
    wordsData.push(record ? record.wordsLearned : 0)
    timeData.push(record ? Math.round(record.timeSpent / 60) : 0) // 转换为分钟
    reviewData.push(record ? record.reviewCount : 0)
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['学习单词', '学习时长(分钟)', '复习次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      }
    ],
    series: [
      {
        name: '学习单词',
        type: 'line',
        data: wordsData,
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '学习时长(分钟)',
        type: 'line',
        data: timeData,
        smooth: true,
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '复习次数',
        type: 'line',
        data: reviewData,
        smooth: true,
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 监听图表周期变化
watch(chartPeriod, () => {
  updateChart()
})

// 页面卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.statistics-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #409EFF;
  margin: 0;
}

.stats-overview {
  margin-bottom: 30px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.words { background: #409EFF; }
.stat-icon.time { background: #67C23A; }
.stat-icon.review { background: #E6A23C; }
.stat-icon.streak { background: #F56C6C; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.calendar-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.calendar-container {
  padding: 10px 0;
}

.calendar-grid {
  width: 100%;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 10px;
}

.day-header {
  text-align: center;
  padding: 10px;
  font-weight: bold;
  color: #606266;
  background: #f5f7fa;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.calendar-day {
  aspect-ratio: 1;
  border: 1px solid #ebeef5;
  padding: 8px;
  cursor: pointer;
  position: relative;
  background: white;
  transition: all 0.2s;
}

.calendar-day:hover {
  background: #f5f7fa;
}

.calendar-day.other-month {
  color: #c0c4cc;
  background: #fafafa;
}

.calendar-day.today {
  border-color: #409EFF;
  background: #ecf5ff;
}

.calendar-day.has-record {
  background: #f0f9ff;
  border-color: #409EFF;
}

.calendar-day.low-activity {
  background: #e1f3d8;
}

.calendar-day.medium-activity {
  background: #b3e19d;
}

.calendar-day.high-activity {
  background: #85ce61;
  color: white;
}

.day-number {
  font-size: 14px;
  font-weight: bold;
}

.activity-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.words-count {
  font-size: 10px;
  background: #409EFF;
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.day-detail {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #606266;
}

.detail-value {
  font-weight: bold;
  color: #303133;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}

@media (max-width: 768px) {
  .statistics-container {
    padding: 15px;
  }
  
  .calendar-day {
    padding: 4px;
  }
  
  .day-number {
    font-size: 12px;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style>
