<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2 class="page-title">学习设置</h2>
      <p class="page-subtitle">自定义您的学习体验</p>
    </div>
    
    <el-card class="settings-card">
      <el-tabs type="border-card" class="settings-tabs">
        <el-tab-pane label="学习设置">
          <el-form 
            :model="settingsForm" 
            label-width="120px"
            class="settings-form"
          >
            <el-form-item label="每日学习单词数">
              <el-slider 
                v-model="settingsForm.dailyWordCount" 
                :min="1" 
                :max="50"
                :step="1"
                :marks="{1: '1', 10: '10', 20: '20', 30: '30', 40: '40', 50: '50'}"
                show-input
                class="settings-slider"
              />
              <p class="setting-description">
                设置每天需要学习的单词数量，建议设置合理数量保持学习动力
              </p>
            </el-form-item>
            
            <el-form-item label="复习频率">
              <el-select 
                v-model="settingsForm.reviewFrequency" 
                placeholder="请选择复习频率"
                class="settings-select"
              >
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
              </el-select>
              <p class="setting-description">
                设置复习单词的频率，复习是巩固记忆的关键
              </p>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSettings" round class="save-button">
                <el-icon><Check /></el-icon> 保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <div class="actions-footer">
      <el-button @click="goBack" round class="back-button" icon="Back">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Check,
  Download,
  Upload,
  Back,
  DocumentAdd,
  Reading
} from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

const store = useStore()
const router = useRouter()
const selectedFiles = ref([])

const settingsForm = reactive({
  dailyWordCount: 10,
  reviewFrequency: 'daily',
  learningMode: 'learn'
})

onMounted(() => {
  const currentSettings = store.state.settings
  settingsForm.dailyWordCount = currentSettings.dailyWordCount
  settingsForm.reviewFrequency = currentSettings.reviewFrequency
  //settingsForm.learningMode = currentSettings.learningMode
})

// 保存设置
const saveSettings = () => {
  store.commit('updateSettings', { ...settingsForm })
  ElMessage({
    message: '设置已保存',
    type: 'success',
    icon: Check
  })
  router.push('/')
}

// 处理导入文件的选择
const handleFileChange = (file, fileList) => {
  selectedFiles.value = fileList.map(f => f.raw)
}

// 从JSON或Excel文件导入单词
const importWords = async () => {
  if (!selectedFiles.value.length) {
    ElMessage.warning('请先选择文件')
    return
  }

  let totalImported = 0
  let totalIgnored = 0
  let totalFiles = 0

  try {
    // 获取当前词库
    const currentWords = store.state.words
    // 创建一个Set来存储已有单词的拼写，用于快速查找
    let existingSpellings = new Set(currentWords.map(word => word.spelling))

    // 存储所有要导入的新单词
    const allNewWords = []

    for (const file of selectedFiles.value) {
      try {
        let data = []
        const fileName = file.name.toLowerCase()

        if (fileName.endsWith('.json')) {
          // 处理JSON文件
          const fileContent = await readFileAsText(file)
          const jsonData = JSON.parse(fileContent)
          if (Array.isArray(jsonData)) {
            data = jsonData
          } else {
            throw new Error('JSON文件格式不正确，应该是单词数组')
          }
        } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
          // 处理Excel文件
          data = await readExcelFile(file)
        } else {
          ElMessage.warning(`不支持的文件格式：${file.name}`)
          continue
        }

        if (data.length > 0) {
          totalFiles++

          // 过滤出尚未存在的单词
          const newWords = data.filter(word => word.spelling && !existingSpellings.has(word.spelling))

          // 更新统计数据
          totalImported += newWords.length
          totalIgnored += data.length - newWords.length

          // 如果有新单词，则收集起来
          if (newWords.length > 0) {
            allNewWords.push(...newWords)

            // 更新存在的拼写集合，防止后续文件中的重复单词被导入
            newWords.forEach(word => existingSpellings.add(word.spelling))
          }
        }
      } catch (error) {
        console.error('解析文件失败：', error)
        ElMessage.error(`解析文件 ${file.name} 失败：${error.message}`)
      }
    }

    // 更新词库
    if (allNewWords.length > 0) {
      // 使用addWords mutation添加所有新单词
      store.commit('addWords', allNewWords)

      ElMessage.success(`已处理 ${totalFiles} 个文件，成功导入 ${totalImported} 个新单词，已忽略 ${totalIgnored} 个已存在的单词`)
    } else {
      ElMessage.info('没有导入新单词，所有单词都已存在')
    }

    // 清空文件选择
    selectedFiles.value = []

  } catch (error) {
    ElMessage.error('导入失败：' + error.message)
  }
}

// 将文件读取为文本的Promise
const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsText(file)
  })
}

// 将文件读取为ArrayBuffer的Promise
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsArrayBuffer(file)
  })
}

// 读取Excel文件并转换为单词数组
const readExcelFile = async (file) => {
  try {
    const arrayBuffer = await readFileAsArrayBuffer(file)
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })

    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]

    // 将工作表转换为JSON数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (jsonData.length === 0) {
      throw new Error('Excel文件为空')
    }

    // 转换为单词对象数组
    const words = []

    // 跳过第一行（可能是标题行）
    const startRow = jsonData[0] && typeof jsonData[0][0] === 'string' &&
                     (jsonData[0][0].toLowerCase().includes('spelling') ||
                      jsonData[0][0].toLowerCase().includes('单词') ||
                      jsonData[0][0].toLowerCase().includes('word')) ? 1 : 0

    for (let i = startRow; i < jsonData.length; i++) {
      const row = jsonData[i]
      if (row && row.length >= 2 && row[0] && row[1]) {
        const word = {
          id: Date.now().toString() + '_' + i,
          spelling: String(row[0]).trim(),
          definition: String(row[1]).trim(),
          example: row[2] ? String(row[2]).trim() : '',
          createdAt: new Date()
        }

        // 只添加有效的单词（拼写和释义都不为空）
        if (word.spelling && word.definition) {
          words.push(word)
        }
      }
    }

    if (words.length === 0) {
      throw new Error('Excel文件中没有找到有效的单词数据。请确保文件包含至少两列：单词拼写和释义')
    }

    return words
  } catch (error) {
    throw new Error(`Excel文件解析失败：${error.message}`)
  }
}
// 返回首页
const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 0;
}

.settings-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  margin-bottom: 8px;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--text-light);
  margin: 0;
  font-size: 1rem;
}

.settings-card {
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: none;
  overflow: hidden;
}

.settings-tabs {
  border: none;
}

.settings-tabs :deep(.el-tabs__header) {
  background: linear-gradient(90deg, var(--bg-light), var(--bg-white));
  margin: 0;
  border-radius: 12px 12px 0 0;
}

.settings-tabs :deep(.el-tabs__item) {
  font-weight: 600;
  padding: 16px 24px;
}

.settings-tabs :deep(.el-tabs__item.is-active) {
  color: var(--primary-color);
}

.settings-form {
  padding: 20px;
}

.settings-slider {
  width: 100%;
  margin-top: 10px;
}

.settings-select {
  width: 100%;
  max-width: 300px;
}

.setting-description {
  font-size: 0.85rem;
  color: var(--text-light);
  margin: 20px 0px 0px 0px;
  line-height: 1.5;
}
.save-button {
  padding: 12px 28px;
  font-weight: 600;
  /* background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); */
  border: none;
  /* box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); */
}

.actions-footer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.back-button {
  padding: 12px 28px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 1.8rem;
  }
  
  .mode-selection {
    flex-direction: column;
  }
  
  .settings-form {
    padding: 15px;
  }
  
  .settings-tabs :deep(.el-tabs__item) {
    padding: 12px 16px;
  }
}
</style>