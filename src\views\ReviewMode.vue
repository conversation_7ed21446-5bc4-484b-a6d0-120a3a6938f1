<template>
  <div class="review-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Refresh /></el-icon>
        复习模式
      </h2>
    </div>

    <!-- 复习进度显示 -->
    <div class="review-progress">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>今日复习进度</span>
            <span class="progress-count">{{ reviewableWords.length }} 个可复习单词</span>
          </div>
        </template>
        
        <el-progress 
          :percentage="reviewProgress" 
          :stroke-width="15"
          :color="getProgressColor"
          class="progress-bar"
        />
        <p class="progress-stats">
          已复习: {{ reviewedCount }} / {{ reviewableWords.length }} 单词
        </p>
      </el-card>
    </div>

    <!-- 日期选择器 -->
    <div class="date-selector">
      <el-card>
        <template #header>
          <span>选择复习日期</span>
        </template>
        
        <div class="date-options">
          <el-checkbox-group v-model="selectedDates" @change="onDateChange">
            <el-checkbox 
              v-for="date in availableDates" 
              :key="date" 
              :label="date"
              class="date-checkbox"
            >
              {{ formatDate(date) }} ({{ getDateWordCount(date) }}个单词)
            </el-checkbox>
          </el-checkbox-group>
        </div>
        
        <div class="date-actions">
          <el-button @click="selectToday" size="small">只选今天</el-button>
          <el-button @click="selectAll" size="small">全选</el-button>
          <el-button @click="clearSelection" size="small">清空</el-button>
        </div>
      </el-card>
    </div>

    <!-- 开始复习按钮 -->
    <div class="review-actions">
      <el-button 
        type="primary" 
        size="large" 
        @click="startReview"
        :disabled="reviewableWords.length === 0"
        round
      >
        <el-icon><VideoPlay /></el-icon>
        开始复习 ({{ reviewableWords.length }}个单词)
      </el-button>
      
      <el-button 
        size="large" 
        @click="goBack"
        round
      >
        返回首页
      </el-button>
    </div>

    <!-- 无可复习内容提示 -->
    <div v-if="reviewableWords.length === 0" class="no-content">
      <el-empty description="暂无可复习内容">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><Clock /></el-icon>
        </template>
        <template #description>
          <p>暂无可复习内容</p>
          <p class="hint">单词学习后1小时才能复习</p>
          <p class="hint">请先学习新单词或等待复习时间到达</p>
        </template>
        <el-button type="primary" @click="goToLearn">去学习新单词</el-button>
      </el-empty>
    </div>

    <!-- 复习统计 -->
    <div v-if="availableDates.length > 0" class="review-stats">
      <el-card>
        <template #header>
          <span>复习统计</span>
        </template>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ totalLearnedWords }}</div>
            <div class="stat-label">总学习单词</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ totalReviewableWords }}</div>
            <div class="stat-label">可复习单词</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ availableDates.length }}</div>
            <div class="stat-label">学习天数</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, VideoPlay, Clock } from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()

// 响应式数据
const selectedDates = ref([])
const reviewedCount = ref(0)

// 计算属性
const availableDates = computed(() => store.getters.getAvailableDates)

const reviewableWords = computed(() => {
  return store.getters.getReviewableWords(selectedDates.value)
})

const totalReviewableWords = computed(() => {
  return store.getters.getReviewableWords([]).length
})

const totalLearnedWords = computed(() => {
  let total = 0
  Object.values(store.state.learningStats.dailyRecords).forEach(record => {
    if (record.learnedWords) {
      total += record.learnedWords.length
    }
  })
  return total
})

const reviewProgress = computed(() => {
  if (reviewableWords.value.length === 0) return 0
  return Math.round((reviewedCount.value / reviewableWords.value.length) * 100)
})

const getProgressColor = computed(() => {
  if (reviewProgress.value < 30) return '#f56c6c'
  if (reviewProgress.value < 70) return '#e6a23c'
  return '#67c23a'
})

// 方法
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (dateStr === today.toISOString().split('T')[0]) {
    return '今天'
  } else if (dateStr === yesterday.toISOString().split('T')[0]) {
    return '昨天'
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

const getDateWordCount = (date) => {
  const record = store.state.learningStats.dailyRecords[date]
  return record && record.learnedWords ? record.learnedWords.length : 0
}

const onDateChange = () => {
  reviewedCount.value = 0
}

const selectToday = () => {
  const today = new Date().toISOString().split('T')[0]
  if (availableDates.value.includes(today)) {
    selectedDates.value = [today]
  } else {
    ElMessage.warning('今天还没有学习记录')
  }
}

const selectAll = () => {
  selectedDates.value = [...availableDates.value]
}

const clearSelection = () => {
  selectedDates.value = []
}

const startReview = () => {
  if (reviewableWords.value.length === 0) {
    ElMessage.warning('请先选择要复习的日期')
    return
  }
  
  // 跳转到学习模式，传递复习参数
  router.push({
    path: '/learn',
    query: { 
      mode: 'review',
      dates: selectedDates.value.join(',')
    }
  })
}

const goBack = () => {
  router.push('/')
}

const goToLearn = () => {
  router.push('/learn')
}

// 生命周期
onMounted(() => {
  // 默认选择今天
  const today = new Date().toISOString().split('T')[0]
  if (availableDates.value.includes(today)) {
    selectedDates.value = [today]
  } else if (availableDates.value.length > 0) {
    // 如果今天没有记录，选择最近的一天
    selectedDates.value = [availableDates.value[0]]
  }
})
</script>

<style scoped>
.review-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #409EFF;
  margin: 0;
}

.review-progress {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.progress-count {
  color: #409EFF;
  font-size: 14px;
}

.progress-bar {
  margin: 15px 0;
}

.progress-stats {
  text-align: center;
  color: #606266;
  margin: 10px 0 0 0;
}

.date-selector {
  margin-bottom: 30px;
}

.date-options {
  margin-bottom: 15px;
}

.date-checkbox {
  display: block;
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.date-checkbox:hover {
  background-color: #f5f7fa;
}

.date-actions {
  display: flex;
  gap: 10px;
}

.review-actions {
  text-align: center;
  margin-bottom: 30px;
}

.review-actions .el-button {
  margin: 0 10px;
}

.no-content {
  margin: 40px 0;
}

.hint {
  color: #909399;
  font-size: 14px;
  margin: 5px 0;
}

.review-stats {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  text-align: center;
}

.stat-item {
  padding: 15px;
  border-radius: 8px;
  background: #f8f9fa;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

@media (max-width: 768px) {
  .review-container {
    padding: 15px;
  }
  
  .date-actions {
    flex-wrap: wrap;
  }
  
  .review-actions .el-button {
    margin: 5px;
    width: 100%;
    max-width: 200px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
  }
}
</style>
