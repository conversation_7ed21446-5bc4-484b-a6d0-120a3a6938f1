import { createApp } from 'vue'
import { createStore } from 'vuex'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'
import App from './App.vue'

import Home from './views/Home.vue'
import AddWord from './views/AddWord.vue'
import LearnMode from './views/LearnMode.vue'
import ReviewMode from './views/ReviewMode.vue'
import Settings from './views/Settings.vue'
import DifficultWords from './views/DifficultWords.vue'
import Statistics from './views/Statistics.vue'


const loadState = () => {
  try {
    const words = localStorage.getItem('myword_words')
    const settings = localStorage.getItem('myword_settings')
    const learningProgress = localStorage.getItem('myword_progress')
    const difficultWords = localStorage.getItem('myword_difficult_words')
    const learningStats = localStorage.getItem('myword_learning_stats')

    return {
      words: words ? JSON.parse(words) : [],
      settings: settings ? JSON.parse(settings) : {
        dailyWordCount: 10,
        reviewFrequency: 'daily',
        learningMode: 'learn'
      },
      learningProgress: learningProgress ? JSON.parse(learningProgress) : {
        dailyCompleted: 0,
        history: []
      },
      difficultWords: difficultWords ? JSON.parse(difficultWords) : [],
      learningStats: learningStats ? JSON.parse(learningStats) : {
        dailyRecords: {}, // 每日学习记录 { '2023-12-07': { wordsLearned: 10, timeSpent: 1800, reviewCount: 5 } }
        weeklyStats: [], // 周统计
        monthlyStats: [], // 月统计
        totalStats: {
          totalWordsLearned: 0,
          totalTimeSpent: 0, // 总学习时间（秒）
          totalReviewCount: 0,
          streakDays: 0, // 连续学习天数
          longestStreak: 0 // 最长连续学习天数
        }
      }
    }
  } catch (e) {
    console.error('Error loading state from localStorage:', e)
    return {
      words: [],
      settings: {
        dailyWordCount: 10,
        reviewFrequency: 'daily',
        learningMode: 'learn'
      },
      learningProgress: {
        dailyCompleted: 0,
        history: []
      },
      difficultWords: [],
      learningStats: {
        dailyRecords: {},
        weeklyStats: [],
        monthlyStats: [],
        totalStats: {
          totalWordsLearned: 0,
          totalTimeSpent: 0,
          totalReviewCount: 0,
          streakDays: 0,
          longestStreak: 0
        }
      }
    }
  }
}

const initialState = loadState()

const store = createStore({
  state() {
    return initialState
  },
  getters: {
    // 获取可复习的单词
    getReviewableWords: (state) => (selectedDates = []) => {
      const now = new Date()
      const reviewableWords = []

      // 如果没有选择日期，默认选择所有日期
      const datesToCheck = selectedDates.length > 0 ? selectedDates : Object.keys(state.learningStats.dailyRecords)

      datesToCheck.forEach(date => {
        const record = state.learningStats.dailyRecords[date]
        if (record && record.learnedWords) {
          record.learnedWords.forEach(wordRecord => {
            const learnedAt = new Date(wordRecord.learnedAt)
            const hoursSinceLearn = (now - learnedAt) / (1000 * 60 * 60) // 小时差

            // 学习后1小时才能复习
            if (hoursSinceLearn >= 1) {
              const word = state.words.find(w => w.id === wordRecord.wordId)
              if (word) {
                reviewableWords.push({
                  ...word,
                  learnedAt: wordRecord.learnedAt,
                  reviewCount: wordRecord.reviewCount || 0,
                  lastReviewAt: wordRecord.lastReviewAt,
                  learnedDate: date
                })
              }
            }
          })
        }
      })

      // 去重（同一个单词可能在多天学习过）
      const uniqueWords = []
      const seenIds = new Set()

      reviewableWords.forEach(word => {
        if (!seenIds.has(word.id)) {
          seenIds.add(word.id)
          uniqueWords.push(word)
        }
      })

      return uniqueWords
    },

    // 获取今日可复习单词数量
    getTodayReviewableCount: (state, getters) => {
      const today = new Date().toISOString().split('T')[0]
      return getters.getReviewableWords([today]).length
    },

    // 获取有学习记录的日期列表
    getAvailableDates: (state) => {
      return Object.keys(state.learningStats.dailyRecords)
        .filter(date => {
          const record = state.learningStats.dailyRecords[date]
          return record && record.learnedWords && record.learnedWords.length > 0
        })
        .sort((a, b) => new Date(b) - new Date(a)) // 按日期倒序
    }
  },
  mutations: {
    addWord(state, word) {
      state.words.push(word);
      localStorage.setItem('myword_words', JSON.stringify(state.words))
    },
    addWords(state, wordsArray) {
      state.words.push(...wordsArray);
      localStorage.setItem('myword_words', JSON.stringify(state.words))
    },
    deleteWord(state, wordId) {
      state.words = state.words.filter(word => word.id !== wordId);
      localStorage.setItem('myword_words', JSON.stringify(state.words))
    },
    updateWord(state, updatedWord) {
      const index = state.words.findIndex(word => word.id === updatedWord.id);
      if (index !== -1) {
        state.words[index] = updatedWord;
        localStorage.setItem('myword_words', JSON.stringify(state.words))
      }
    },
    updateSettings(state, settings) {
      state.settings = { ...state.settings, ...settings };
      localStorage.setItem('myword_settings', JSON.stringify(state.settings))
    },
    updateProgress(state, progress) {
      state.learningProgress = { ...state.learningProgress, ...progress };
      const today = new Date().toISOString().split('T')[0];
      const historyIndex = state.learningProgress.history.findIndex(item => item.date === today);
      if (historyIndex !== -1) {
        state.learningProgress.history[historyIndex].count = progress.dailyCompleted;
      } else {
        state.learningProgress.history.push({
          date: today,
          count: progress.dailyCompleted
        });
      }
      localStorage.setItem('myword_progress', JSON.stringify(state.learningProgress))
    },
    // 生词本相关mutations
    addToDifficultWords(state, word) {
      // 检查是否已经在生词本中
      const exists = state.difficultWords.find(w => w.id === word.id)
      if (!exists) {
        const difficultWord = {
          ...word,
          addedToDifficultAt: new Date(),
          reviewCount: 0,
          lastReviewAt: null
        }
        state.difficultWords.push(difficultWord)
        localStorage.setItem('myword_difficult_words', JSON.stringify(state.difficultWords))
      }
    },
    removeFromDifficultWords(state, wordId) {
      state.difficultWords = state.difficultWords.filter(word => word.id !== wordId)
      localStorage.setItem('myword_difficult_words', JSON.stringify(state.difficultWords))
    },
    updateDifficultWordProgress(state, { wordId, reviewCount, lastReviewAt }) {
      const word = state.difficultWords.find(w => w.id === wordId)
      if (word) {
        word.reviewCount = reviewCount || word.reviewCount + 1
        word.lastReviewAt = lastReviewAt || new Date()
        localStorage.setItem('myword_difficult_words', JSON.stringify(state.difficultWords))
      }
    },
    // 学习统计相关mutations
    recordLearningSession(state, { wordsLearned, timeSpent, reviewCount, date }) {
      const today = date || new Date().toISOString().split('T')[0]

      if (!state.learningStats.dailyRecords[today]) {
        state.learningStats.dailyRecords[today] = {
          wordsLearned: 0,
          timeSpent: 0,
          reviewCount: 0,
          date: today
        }
      }

      // 更新当日记录
      state.learningStats.dailyRecords[today].wordsLearned += wordsLearned || 0
      state.learningStats.dailyRecords[today].timeSpent += timeSpent || 0
      state.learningStats.dailyRecords[today].reviewCount += reviewCount || 0

      // 更新总统计
      state.learningStats.totalStats.totalWordsLearned += wordsLearned || 0
      state.learningStats.totalStats.totalTimeSpent += timeSpent || 0
      state.learningStats.totalStats.totalReviewCount += reviewCount || 0

      // 同步更新今日学习进度 - 只有学习新单词时才更新dailyCompleted
      if (wordsLearned > 0) {
        state.learningProgress.dailyCompleted = state.learningStats.dailyRecords[today].wordsLearned

        // 更新历史记录
        const historyIndex = state.learningProgress.history.findIndex(item => item.date === today)
        if (historyIndex !== -1) {
          state.learningProgress.history[historyIndex].count = state.learningProgress.dailyCompleted
        } else {
          state.learningProgress.history.push({
            date: today,
            count: state.learningProgress.dailyCompleted
          })
        }
        localStorage.setItem('myword_progress', JSON.stringify(state.learningProgress))
      }

      // 计算连续学习天数
      this.commit('updateStreakDays')

      localStorage.setItem('myword_learning_stats', JSON.stringify(state.learningStats))
    },

    // 实时更新单个学习记录（用于学习过程中的实时更新）
    updateSingleLearningRecord(state, { wordsLearned, reviewCount, date, wordId }) {
      const today = date || new Date().toISOString().split('T')[0]

      if (!state.learningStats.dailyRecords[today]) {
        state.learningStats.dailyRecords[today] = {
          wordsLearned: 0,
          timeSpent: 0,
          reviewCount: 0,
          date: today,
          learnedWords: [] // 记录当天学习的单词ID和时间
        }
      }

      // 更新当日记录
      state.learningStats.dailyRecords[today].wordsLearned += wordsLearned || 0
      state.learningStats.dailyRecords[today].reviewCount += reviewCount || 0

      // 如果是学习新单词，记录单词ID和学习时间
      if (wordsLearned > 0 && wordId) {
        const learnedWords = state.learningStats.dailyRecords[today].learnedWords || []
        const existingIndex = learnedWords.findIndex(item => item.wordId === wordId)

        if (existingIndex === -1) {
          learnedWords.push({
            wordId: wordId,
            learnedAt: new Date().toISOString(),
            reviewCount: 0,
            lastReviewAt: null
          })
        }
        state.learningStats.dailyRecords[today].learnedWords = learnedWords
      }

      // 如果是复习，更新复习记录
      if (reviewCount > 0 && wordId) {
        // 查找所有日期中的该单词记录
        Object.keys(state.learningStats.dailyRecords).forEach(dateKey => {
          const record = state.learningStats.dailyRecords[dateKey]
          if (record.learnedWords) {
            const wordRecord = record.learnedWords.find(item => item.wordId === wordId)
            if (wordRecord) {
              wordRecord.reviewCount = (wordRecord.reviewCount || 0) + 1
              wordRecord.lastReviewAt = new Date().toISOString()
            }
          }
        })
      }

      // 更新总统计
      state.learningStats.totalStats.totalWordsLearned += wordsLearned || 0
      state.learningStats.totalStats.totalReviewCount += reviewCount || 0

      // 同步更新今日学习进度 - 只有学习新单词时才更新dailyCompleted
      if (wordsLearned > 0) {
        state.learningProgress.dailyCompleted = state.learningStats.dailyRecords[today].wordsLearned

        // 更新历史记录
        const historyIndex = state.learningProgress.history.findIndex(item => item.date === today)
        if (historyIndex !== -1) {
          state.learningProgress.history[historyIndex].count = state.learningProgress.dailyCompleted
        } else {
          state.learningProgress.history.push({
            date: today,
            count: state.learningProgress.dailyCompleted
          })
        }
        localStorage.setItem('myword_progress', JSON.stringify(state.learningProgress))
      }

      localStorage.setItem('myword_learning_stats', JSON.stringify(state.learningStats))
    },

    updateStreakDays(state) {
      const records = state.learningStats.dailyRecords
      const dates = Object.keys(records).sort()

      if (dates.length === 0) {
        state.learningStats.totalStats.streakDays = 0
        return
      }

      let currentStreak = 0
      let maxStreak = 0
      const today = new Date().toISOString().split('T')[0]

      // 从今天开始往前计算连续天数
      let checkDate = new Date(today)
      while (true) {
        const dateStr = checkDate.toISOString().split('T')[0]
        if (records[dateStr] && records[dateStr].wordsLearned > 0) {
          currentStreak++
          maxStreak = Math.max(maxStreak, currentStreak)
        } else {
          break
        }
        checkDate.setDate(checkDate.getDate() - 1)
      }

      state.learningStats.totalStats.streakDays = currentStreak
      state.learningStats.totalStats.longestStreak = Math.max(
        state.learningStats.totalStats.longestStreak,
        maxStreak
      )
    },

    // 同步学习进度和统计数据
    syncLearningProgress(state) {
      const today = new Date().toISOString().split('T')[0]
      const todayRecord = state.learningStats.dailyRecords[today]

      if (todayRecord && todayRecord.wordsLearned > 0) {
        // 同步今日学习进度
        state.learningProgress.dailyCompleted = todayRecord.wordsLearned

        // 更新历史记录
        const historyIndex = state.learningProgress.history.findIndex(item => item.date === today)
        if (historyIndex !== -1) {
          state.learningProgress.history[historyIndex].count = state.learningProgress.dailyCompleted
        } else {
          state.learningProgress.history.push({
            date: today,
            count: state.learningProgress.dailyCompleted
          })
        }
        localStorage.setItem('myword_progress', JSON.stringify(state.learningProgress))
      }
    },

    resetState(state) {
      state.words = [];
      state.settings = {
        dailyWordCount: 10,
        reviewFrequency: 'daily',
        learningMode: 'learn'
      };
      state.learningProgress = {
        dailyCompleted: 0,
        history: []
      };
      state.difficultWords = [];
      state.learningStats = {
        dailyRecords: {},
        weeklyStats: [],
        monthlyStats: [],
        totalStats: {
          totalWordsLearned: 0,
          totalTimeSpent: 0,
          totalReviewCount: 0,
          streakDays: 0,
          longestStreak: 0
        }
      };
      localStorage.removeItem('myword_words');
      localStorage.removeItem('myword_settings');
      localStorage.removeItem('myword_progress');
      localStorage.removeItem('myword_difficult_words');
      localStorage.removeItem('myword_learning_stats');
    }
  }
})

const routes = [
  { path: '/', component: Home },
  { path: '/add', component: AddWord },
  { path: '/edit/:id', component: AddWord },
  { path: '/learn', component: LearnMode },
  { path: '/review', component: ReviewMode },
  { path: '/difficult', component: DifficultWords },
  { path: '/statistics', component: Statistics },
  { path: '/settings', component: Settings },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(store)
app.use(router)
app.use(ElementPlus)

// 应用启动时同步学习进度数据
store.commit('syncLearningProgress')

app.mount('#app')
