<template>
  <div class="difficult-words-container">
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Collection /></el-icon>
        我的生词本
      </h2>
      <div class="header-stats">
        <el-tag type="info" size="large">
          共 {{ difficultWords.length }} 个生词
        </el-tag>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="difficultWords.length === 0" class="empty-state">
      <el-empty description="生词本还是空的">
        <template #image>
          <el-icon size="100" color="#c0c4cc"><Collection /></el-icon>
        </template>
        <template #description>
          <p>还没有添加任何生词</p>
          <p>在学习过程中遇到困难的单词可以添加到生词本</p>
        </template>
        <template #extra>
          <el-button type="primary" @click="goToLearn">
            <el-icon><Reading /></el-icon>
            开始学习
          </el-button>
        </template>
      </el-empty>
    </div>

    <!-- 生词列表 -->
    <div v-else class="words-content">
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button 
            type="primary" 
            @click="startDifficultWordsReview"
            :disabled="difficultWords.length === 0"
          >
            <el-icon><VideoPlay /></el-icon>
            开始复习生词
          </el-button>
        </div>
        <div class="right-actions">
          <el-button 
            type="danger" 
            plain 
            @click="clearAllDifficultWords"
            :disabled="difficultWords.length === 0"
          >
            <el-icon><Delete /></el-icon>
            清空生词本
          </el-button>
        </div>
      </div>

      <!-- 生词卡片列表 -->
      <div class="words-grid">
        <div 
          v-for="word in difficultWords" 
          :key="word.id" 
          class="word-card"
        >
          <el-card shadow="hover" class="word-item">
            <template #header>
              <div class="card-header">
                <span class="word-spelling">{{ word.spelling }}</span>
                <el-button 
                  type="danger" 
                  size="small" 
                  circle 
                  @click="removeFromDifficult(word.id)"
                  class="remove-btn"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </template>
            
            <div class="word-content">
              <div class="word-definition">
                <strong>释义：</strong>{{ word.definition }}
              </div>
              
              <div v-if="word.example" class="word-example">
                <strong>例句：</strong>{{ word.example }}
              </div>
              
              <div class="word-stats">
                <el-tag size="small" type="info">
                  复习次数: {{ word.reviewCount || 0 }}
                </el-tag>
                <el-tag size="small" type="warning" v-if="word.lastReviewAt">
                  上次复习: {{ formatDate(word.lastReviewAt) }}
                </el-tag>
              </div>
            </div>
            
            <template #footer>
              <div class="card-actions">
                <el-button 
                  size="small" 
                  @click="playPronunciation(word.spelling)"
                  circle
                >
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click="reviewSingleWord(word)"
                >
                  单独复习
                </el-button>
              </div>
            </template>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Collection, 
  Reading, 
  VideoPlay, 
  Delete, 
  Close 
} from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()

// 获取生词列表
const difficultWords = computed(() => store.state.difficultWords)

// 从生词本移除单词
const removeFromDifficult = async (wordId) => {
  try {
    await ElMessageBox.confirm(
      '确定要从生词本中移除这个单词吗？',
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    store.commit('removeFromDifficultWords', wordId)
    ElMessage.success('已从生词本移除')
  } catch (error) {
    // 用户取消操作
  }
}

// 清空生词本
const clearAllDifficultWords = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空整个生词本吗？此操作不可恢复！',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 逐个移除所有生词
    const wordIds = difficultWords.value.map(word => word.id)
    wordIds.forEach(id => {
      store.commit('removeFromDifficultWords', id)
    })
    
    ElMessage.success('生词本已清空')
  } catch (error) {
    // 用户取消操作
  }
}

// 开始生词复习
const startDifficultWordsReview = () => {
  if (difficultWords.value.length === 0) {
    ElMessage.warning('生词本为空，无法开始复习')
    return
  }
  
  // 跳转到学习模式，并传递生词本标识
  router.push('/learn?mode=difficult')
}

// 单独复习某个单词
const reviewSingleWord = (word) => {
  // 跳转到学习模式，并传递单个单词ID
  router.push(`/learn?mode=single&wordId=${word.id}`)
}

// 播放发音
const playPronunciation = (word) => {
  const utterance = new SpeechSynthesisUtterance(word)
  utterance.lang = 'en-US'
  window.speechSynthesis.speak(utterance)
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 跳转到学习页面
const goToLearn = () => {
  router.push('/learn')
}
</script>

<style scoped>
.difficult-words-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #409EFF;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.words-content {
  animation: fadeIn 0.3s ease-in;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.left-actions, .right-actions {
  display: flex;
  gap: 10px;
}

.words-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.word-card {
  transition: transform 0.2s ease;
}

.word-card:hover {
  transform: translateY(-2px);
}

.word-item {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-spelling {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.remove-btn {
  opacity: 0.7;
}

.remove-btn:hover {
  opacity: 1;
}

.word-content {
  margin: 15px 0;
}

.word-definition {
  margin-bottom: 10px;
  line-height: 1.5;
}

.word-example {
  margin-bottom: 15px;
  color: #666;
  font-style: italic;
  line-height: 1.5;
}

.word-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .difficult-words-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .action-bar {
    flex-direction: column;
    gap: 15px;
  }
  
  .words-grid {
    grid-template-columns: 1fr;
  }
}
</style>
