<template>
  <div class="home-container">
    <!-- 搜索栏 -->
    <div class="search-container">
      <el-input
        v-model="searchQuery"
        placeholder="搜索单词或释义"
        class="search-input"
        clearable
      >
        <template #prefix>
          <el-icon class="search-icon"><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 单词列表 -->
    <div class="word-list-container">
      <el-empty v-if="filteredWords.length === 0" description="暂无单词，请添加新单词">
        <el-button type="primary" round @click="goToAdd">
          <el-icon><Plus /></el-icon>
          <span style="margin-left: 4px">添加单词</span>
        </el-button>
      </el-empty>
      
      <transition-group name="list" tag="div" class="word-grid">
        <el-card v-for="word in filteredWords" :key="word.id" class="word-item">
          <div class="word-header">
            <h3 class="word-spelling">{{ word.spelling }}</h3>
            <div class="word-actions">
              <el-button type="text" @click="playPronunciation(word.spelling)" class="action-icon">
                <el-icon><VideoPlay /></el-icon>
              </el-button>
              <router-link :to="`/edit/${word.id}`">
                <el-button type="text" class="action-icon">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </router-link>
              <el-button type="text" @click="confirmDelete(word)" class="action-icon">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
          <p class="word-definition">{{ word.definition }}</p>
          <p v-if="word.example" class="word-example">{{ word.example }}</p>
          
          <div class="word-badges">
            <el-tag size="small" effect="light" class="mastery-tag" 
                  :type="getMasteryTagType(word.mastery || 0)">
              熟练度: {{ word.mastery || 0 }}%
            </el-tag>
          </div>
        </el-card>
      </transition-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar">
      <div class="progress-container">
        <h4 class="progress-title">今日学习进度</h4>
        <el-progress 
          :percentage="dailyProgress" 
          :stroke-width="15"
          :color="getProgressColor"
          class="progress-bar"
        />
        <p class="progress-stats">
          已学习: {{ store.state.learningProgress.dailyCompleted }} / {{ store.state.settings.dailyWordCount }} 单词
        </p>
      </div>
      <div class="action-buttons">
        <el-button type="primary" @click="startLearning" round icon="VideoCameraFilled">开始学习</el-button>
        <el-button
          type="info"
          @click="startReview"
          round
          icon="Refresh"
          :disabled="todayReviewableCount === 0"
        >
          开始复习 ({{ todayReviewableCount }})
        </el-button>
        <el-button
          type="warning"
          @click="goToDifficultWords"
          round
          icon="Collection"
          :disabled="difficultWordsCount === 0"
        >
          生词本 ({{ difficultWordsCount }})
        </el-button>
        <el-button
          type="success"
          @click="goToStatistics"
          round
          icon="TrendCharts"
        >
          学习统计
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { VideoPlay, Edit, Delete, Plus, Search, Refresh } from '@element-plus/icons-vue'
import { Howl } from 'howler'

const store = useStore()
const router = useRouter()
const searchQuery = ref('')

// 根据搜索查询计算过滤后的单词
const filteredWords = computed(() => {
  const words = store.state.words
  if (!searchQuery.value) return words
  
  const query = searchQuery.value.toLowerCase()
  return words.filter(word => 
    word.spelling.toLowerCase().includes(query) || 
    word.definition.toLowerCase().includes(query)
  )
})

// 计算每日学习进度
const dailyProgress = computed(() => {
  const completed = store.state.learningProgress.dailyCompleted
  const target = store.state.settings.dailyWordCount
  return Math.min(Math.round((completed / target) * 100), 100)
})

// 计算生词本单词数量
const difficultWordsCount = computed(() => {
  return store.state.difficultWords.length
})

// 计算今日可复习单词数量
const todayReviewableCount = computed(() => {
  return store.getters.getTodayReviewableCount
})

// 动态颜色
const getProgressColor = computed(() => {
  if (dailyProgress.value < 30) return '#f56c6c'
  if (dailyProgress.value < 70) return '#e6a23c'
  return '#67c23a'
})

// 根据熟练度返回不同的类型
const getMasteryTagType = (mastery) => {
  if (mastery < 30) return 'danger'
  if (mastery < 70) return 'warning'
  return 'success'
}

// 使用文本转语音播放单词发音
const playPronunciation = (word) => {
  const utterance = new SpeechSynthesisUtterance(word)
  utterance.lang = 'en-US'
  window.speechSynthesis.speak(utterance)
}

// 删除单词确认
const confirmDelete = (word) => {
  ElMessageBox.confirm(
    `确定要删除单词 "${word.spelling}" 吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    store.commit('deleteWord', word.id)
  }).catch(() => {
    // 用户取消操作
  })
}

// 开始学习
const startLearning = () => {
  router.push('/learn')
}

// 前往添加单词页面
const goToAdd = () => {
  router.push('/add')
}

const goToDifficultWords = () => {
  router.push('/difficult')
}

const goToStatistics = () => {
  router.push('/statistics')
}

const startReview = () => {
  if (todayReviewableCount.value === 0) {
    ElMessageBox.alert('暂无可复习内容，请先学习新单词', '提示', {
      confirmButtonText: '确定',
      type: 'info'
    })
    return
  }
  router.push('/review')
}
</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
  padding: 0;
}

.search-container {
  margin-bottom: 20px;
}

.search-input {
  border-radius: 20px;
  transition: all 0.3s ease;
}

.search-input:focus-within {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.search-icon {
  color: var(--text-light);
  margin-right: 8px;
}

.word-list-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  padding: 5px;
}

.word-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.word-item {
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 0;
  position: relative;
  overflow: hidden;
  border: none;
  height: 100%;
}

.word-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.word-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.word-spelling {
  margin: 0;
  font-size: 1.4rem;
  color: var(--primary-color);
  font-weight: 600;
}

.word-definition {
  margin: 10px 0;
  color: var(--text-secondary);
  line-height: 1.4;
}

.word-example {
  font-style: italic;
  color: var(--text-light);
  margin: 12px 0 15px 0;
  padding: 10px;
  background-color: var(--bg-light);
  border-radius: 6px;
  border-left: 3px solid var(--primary-light);
}

.word-actions {
  display: flex;
  gap: 2px;
}

.action-icon {
  padding: 8px;
  transition: color 0.2s ease;
}

.action-icon:hover {
  color: var(--primary-color);
}

.word-badges {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mastery-tag {
  font-size: 11px;
}

.bottom-action-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
  background-color: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.progress-container {
  width: 100%;
  padding: 0 15px;
}

.progress-title {
  text-align: left;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-secondary);
}

.progress-stats {
  text-align: right;
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 5px 0 0 0;
}

.progress-bar {
  width: 100%;
}

/* 过渡效果 */
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

/* 响应式设计媒体查询 */
@media (max-width: 768px) {
  .word-grid {
    grid-template-columns: 1fr;
  }
  
  .home-container {
    height: calc(100vh - 180px);
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}
</style> 